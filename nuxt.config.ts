// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: false },
  app: {
    head: {
      title: '标信查平台_国家标准_行业标准_地方标准_团体标准_标准查询_标准应用',
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
        {
          name: 'description',
          content:
            '标信查平台涵盖了国家、国际、行业、地方、企业、团体等上百万条权威标准数据，实时动态更新，提供标准查询、智能编辑器、标准查新与专题订阅、标准化管理平台等标准化信息服务，供各大行业用户灵活选择！',
        },
        { name: 'keywords', content: '标信查，国家标准，行业标准，地方标准，团体标准，标准资讯，标准查询' },
        { charset: 'utf-8' },
      ],
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
  },
  runtimeConfig: {
    // 私有密钥仅在服务器端可用
    apiSecret: '123',

    // 对客户端暴露的公共密钥
    public: {
      baseURL: `${process.env.BASE_URL}/cloud`,
      kkFileURL: process.env.APP_KKFILE,
      webSocketURL: process.env.SOCKET_URL,
    },
  },
  css: ['element-plus/dist/index.css', '@/assets/styles/index.scss'],
  nitro: {
    devProxy: {
      '/api': {
        target: `${process.env.BASE_URL}/cloud`,
        changeOrigin: true,
      },
    },
  },
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/styles/element-variables.scss" as *;@import "@/assets/styles/variables.scss";`,
        },
      },
    },
  },
  modules: ['@element-plus/nuxt', '@pinia/nuxt', 'nuxt-echarts'],
  elementPlus: { defaultLocale: 'zh-cn' },
});
