<template>
  <div class="membership-item-subitem h-154">
    <div>
      <div class="f-16 c-99">
        <span class="f-36 c-primary f-bold">{{ row.originalPrice - row.discount }}</span>
        /年
      </div>
      <div v-if="row.discount" class="f-14 c-99">
        原价：
        <span class="line-through">{{ row.originalPrice }}元</span>
      </div>
      <div @click.stop="handlePay(row)" class="membership-item-subitem-btn">立即开通</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { IMemberPackage } from '@/types/index';

  const emit = defineEmits(['pay']);

  const props = withDefaults(
    defineProps<{
      row: IMemberPackage;
    }>(),
    {
      row: () => ({
        id: 0,
        originalPrice: 0,
        discount: 0,
        package: [],
      }),
    }
  );

  const { row } = toRefs(props);

  const handlePay = (row: IMemberPackage) => {
    emit('pay', row);
  };
</script>

<style scoped lang="scss">
  .membership {
    &-item {
      &-subitem-btn {
        color: #fff;
        width: 111px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        background: linear-gradient(-90deg, #ae00ff, #045cff);
        box-shadow: 0px 4px 3px 0px rgba(24, 53, 108, 0.26);
        border-radius: 5px;
        margin-top: 13px;
        cursor: pointer;
      }
    }
  }

  .h-154 {
    height: 154px !important;
    line-height: normal !important;
    text-align: center;
  }
</style>
