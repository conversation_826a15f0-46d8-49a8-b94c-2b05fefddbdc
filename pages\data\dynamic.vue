<template>
  <div>
    <div class="banner">
      <div class="main-wrap">
        <div class="f-32 c-33 f-bold">标准动态</div>
        <div class="banner-title mt-20 lh-26">标准数据动态监测，提供最新、最实时的标准变化信息。</div>
      </div>
    </div>
    <div class="content main-wrap">
      <div class="content-type">
        <div class="flex flex-ai-center">
          <div
            @click="handleCut(item)"
            v-for="(item, index) in typeList"
            :key="index"
            class="content-type-item"
            :class="item.value == activeType ? 'content-type-active' : ''"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
      <template v-if="tableData && tableData.length > 0">
        <div class="content-standard">
          <span v-for="item in standardTypeOptions" :key="item.dictValue">
            <template v-if="item.standardCount">
              {{ item.dictLabel }}:
              <span class="c-primary">{{ item.standardCount }}</span>
              &nbsp;&nbsp;&nbsp;
            </template>
          </span>
        </div>
        <div
          v-for="(item, index) in tableData"
          :key="item.id"
          @click="handleJump('/retrieval/domesticDetail', { id: item.id })"
          class="content-card"
        >
          <div class="content-card-info">
            <div class="content-card-info-number">
              {{ (form.pageNum - 1) * form.pageSize + index + 1 }}
            </div>
            <div class="content-card-info-title">
              <div class="flex flex-ai-center overflow-ellipsis">
                <div class="f-16 c-33 f-bold overflow-ellipsis">{{ item.standardCode }}&nbsp;|&nbsp;{{ item.standardName }}</div>
                <div :class="getStatusColor('blue')" class="ml-15">{{ item.standardTypeName }}</div>
                <div :class="getStatusColor(statusToString(item.standardStatus))" class="ml-10">
                  {{ item.standardStatusName }}
                </div>
              </div>
              <div class="f-14 c-33 mt-15 flex flex-1">
                <div class="w-20vw overflow-ellipsis">发布日期：{{ item.publishDate || '-' }}</div>
                <div class="w-20vw overflow-ellipsis">实施日期：{{ item.executeDate || '-' }}</div>
                <div v-if="item.repealDate" class="w-20vw overflow-ellipsis">废止日期：{{ item.repealDate || '-' }}</div>
                <div v-if="item.beReplacedStandardCode" class="w-20vw overflow-ellipsis">
                  被替代标准：{{ item.beReplacedStandardCode || '-' }}
                </div>
              </div>
            </div>
          </div>
          <span
            v-if="activeType != 4"
            @click.stop="handleTrusteeship(item)"
            class="iconfont content-card-icon c-88"
            :class="item.trusteeshipType ? 'icon-kuangjituoguan c-primary' : 'icon-tuoguan c-88'"
          ></span>
        </div>
        <BxcPagination
          v-model:page="form.pageNum"
          v-model:limit="form.pageSize"
          :total="tableTotal"
          @pagination="getData"
          class="mt-35"
        />
      </template>
      <BxcEmpty v-else />
    </div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" />
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '标准动态_标准发布动态_标准废止动态_标准替代动态_标信查平台',
    meta: [
      { name: 'keywords', content: '标准动态，标准发布动态，标准废止动态，标准替代动态' },
      {
        name: 'description',
        content:
          '标信查平台数据服务频道，可以为企业提供标准托管、标准查新、标准情报、标准灯塔、标准战略分析等多种标准信息化服务，助力广大企业更好发展。',
      },
    ],
  });

  import { ElMessageBox } from 'element-plus';
  import { getDicts } from '@/api/common';
  import { getDynamicList, getStatistics } from '@/api/data/dynamic';
  import { getStatusColor, handleJump } from '@/utils/common';
  import { type IStandardInfo, type IDicts, type IResponseData } from '@/types';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();
  const { $modal } = useNuxtApp();
  const route = useRoute();

  const activeType = ref<string | number>(0);
  const standardTypeOptions = ref<IDicts[]>([]);
  const allTotal = ref(0);
  const typeList = reactive([
    { title: '最新发布标准', value: 0 },
    { title: '即将实施标准', value: 1 },
    { title: '最新实施标准', value: 2 },
    { title: '即将废止标准', value: 3 },
    { title: '最新废止标准', value: 4 },
  ]);
  const statisticsInfo = ref([]);
  const form = ref<{
    pageNum: number;
    pageSize: number;
    queryDynamicType: number | string;
  }>({
    pageNum: 1,
    pageSize: 10,
    queryDynamicType: 0,
  });
  const tableData = ref<IStandardInfo[]>([]);
  const tableTotal = ref(0);
  const openLogin = ref(false);

  if (route.query.type) {
    activeType.value = route.query?.type as string;
    form.value.queryDynamicType = route.query?.type as string;
  }

  let tableRes = <IResponseData>await getDynamicList(form.value);
  tableData.value = tableRes.rows || [];
  tableTotal.value = tableRes.total || 0;

  let statisticsRes = <IResponseData>await getStatistics({ queryDynamicType: 0 });
  statisticsInfo.value = statisticsRes.data;

  let dictRes = <IResponseData>await getDicts('bxc_standard_type');
  standardTypeOptions.value = dictRes.data.filter((item: any) => item.dictValue != 5 && item.dictValue != 6);

  const pooledData = () => {
    let result: any = statisticsInfo.value.find((item: any) => item.standardType == '-1');
    if (result) allTotal.value = result.standardCount;
    standardTypeOptions.value = standardTypeOptions.value.map(standardTypeItem => {
      const match: any = statisticsInfo.value.find(
        (statisticsItem: any) => statisticsItem.standardType == standardTypeItem.dictValue
      );
      if (match) {
        standardTypeItem.standardCount = match.standardCount;
      }
      return standardTypeItem;
    });
  };

  pooledData();

  const statusToString = (status: number | string) => {
    switch (Number(status)) {
      case 0:
        return 'sky-blue';
        break;
      case 1:
        return 'green';
        break;
      case 2:
        return 'blue';
        break;
      case 3:
        return 'gray';
        break;
      case 4:
        return 'red';
        break;
      default:
        return 'blue';
        break;
    }
  };

  const handleCut = (row: any) => {
    activeType.value = row.value;
    form.value.queryDynamicType = row.value;
    getData('pageNum');
  };

  const getData = async (pageNum?: string) => {
    if (pageNum) form.value.pageNum = 1;
    let { rows, total } = <IResponseData>await useHttp.get('/search/sdc/stdStandard/dynamic', form.value);
    tableData.value = rows || [];
    tableTotal.value = total || 0;
    let { data } = <IResponseData>await useHttp.get('/search/sdc/stdStandard/dynamicTop', { queryDynamicType: activeType.value });
    statisticsInfo.value = data;
    pooledData();
  };

  const handleTrusteeship = async (row: any) => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      if (row.trusteeshipType == 1) {
        ElMessageBox.confirm('确认取消托管标准【' + row.standardCode + '】？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          lockScroll: false,
        })
          .then(() => {
            useHttp.delete('/search/trusteeshipManage/remove', { ids: [row.id] }).then(res => {
              let data = res as IResponseData;
              if (data.code == 200) {
                $modal.msgSuccess('取消标准托管成功！');
                getData('pageNum');
              }
            });
          })
          .catch(() => {});
      } else {
        let data = <IResponseData>await useHttp.post('/search/trusteeshipManage', {
          standardId: row.id,
          standardCode: row.standardCode,
          standardType: row.standardType,
        });
        if (data.code == 200) {
          $modal.msgSuccess('标准托管成功！');
          getData('pageNum');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .banner {
    width: 100%;
    min-height: 380px;
    background: url('@/assets/images/dynamic/cover.png') no-repeat center;
    background-size: 100% 100%;

    .main-wrap {
      padding-top: 165px;
    }

    &-title {
      font-size: 16px;
      color: #333;
      width: 750px;
      line-height: 24px;
    }
  }

  .content {
    padding: 40px 0 65px;
    box-sizing: border-box;

    &-type {
      border-bottom: 1px solid #e8e8e8;

      &-item {
        padding: 10px 0;
        font-size: 16px;
        color: #333;
        cursor: pointer;
        border-bottom: 3px solid #fff;

        &:not(:first-child) {
          margin-left: 40px;
        }
      }

      &-active {
        font-weight: bold;
        color: $primary-color;
        border-bottom-color: $primary-color;
      }
    }

    &-standard {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333;
      margin: 20px 0 10px;
      padding: 13px 11px;
      box-sizing: border-box;
      background-color: #eef5ff;
      border-radius: 2px;
    }

    &-card {
      padding: 15px 0;
      box-sizing: border-box;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-info {
        display: flex;
        align-items: center;
        width: 85%;
        overflow: hidden;
        cursor: pointer;

        &-number {
          width: 55px;
          text-align: center;
          font-size: 16px;
        }

        &-title {
          width: calc(100% - 55px);
          flex: 1;
          overflow: hidden;
        }
      }

      &-icon {
        font-size: 20px;
        display: block;
        cursor: pointer;
        margin-right: 20px;
      }
    }
  }

  .w-20vw {
    width: 19.5%;
  }
</style>
