<template>
  <el-popover v-model="visible" popper-class="nav-user-wrap" placement="bottom" trigger="hover" :teleported="false"
    :offset="13" :show-arrow="false" ref="navUserRef">
    <div class="nav-user-container scroller-bar-style">
      <div class="profile-wrap">
        <img :src="userStore.avatar" alt="">
        <div class="flex-1 ml-10">
          <div class="flex flex-ai-center">
            <span class="f-16 f-bold">{{ userStore.nickName }}</span>
            <div class="noVip">未开通会员</div>
            <el-tooltip effect="dark" content="企业会员" placement="top">
              <img src="@/assets/images/user/vip1.png" alt="" class="vip-icon">
            </el-tooltip>
            <el-tooltip effect="dark" content="企业认证" placement="top">
              <img src="@/assets/images/user/vip2.png" alt="" class="vip-icon">
            </el-tooltip>
            <el-tooltip effect="dark" content="权益包" placement="top">
              <img src="@/assets/images/user/vip3.png" alt="" class="vip-icon">
            </el-tooltip>
            <el-tooltip effect="dark" content="个人会员" placement="top">
              <img src="@/assets/images/user/vip4.png" alt="" class="vip-icon">
            </el-tooltip>
          </div>
          <div class="flex flex-ai-center mt-10">
            <NuxtLink to="" class="c-primary pointer" style="height: auto;line-height: normal">
              开通会员>
            </NuxtLink>
            <span>&nbsp;解锁更多权益</span>
          </div>
          <div class="mt-10 c-99 mb-10">会员有效期：2012-01-01至2025-12-31</div>
        </div>
      </div>
      <div class="enter-center">
        <NuxtLink to="" @click.prevent="handleLink({ path: '/user-center/index' })">
          进入用户中心>>
        </NuxtLink>
      </div>
      <div class="line"></div>
      <div class="tool-wrap">
        <div class="tool-title">常用工具</div>
        <div class="tool-list">
          <div v-for="(item, index) in dataList" :key="index" @click="handleLink(item)" class="tool-item">
            <i :class="item.icon"></i>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
        <div class="flex flex-jc-end flex-ai-center c-88 f-14 mt-10 pointer" @click="logout">
          <i class="iconfont icon-tuichudenglu"></i>
          <span class="ml-5">退出登录</span>
        </div>
      </div>
    </div>
    <template #reference>
      <div class="flex">
        <img :src="userStore.avatar" alt="" class="header-avatar">
      </div>
    </template>
  </el-popover>

</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/userStore'

const userStore = useUserStore()
const navUserRef = ref()
const visible = ref(false)
const { $modal } = useNuxtApp()
const router = useRouter()

const dataList = [
  {
    name: '标准托管',
    icon: 'iconfont icon-kuangjituoguan',
    path: '/user-center/trusteeship/index',
  },
  {
    name: '我的收藏',
    icon: 'iconfont icon-shoucang2',
    path: '/user-center/collect/index',
  },
  {
    name: '订阅体系',
    icon: 'iconfont icon-yidingyue',
    path: '/user-center/standard-system/subscription',
  },
  {
    name: '我的体系',
    icon: 'iconfont icon-tixi',
    path: '/user-center/standard-system/my',
  },
]


const handleLink = (row: any) => {
  if (!row.path) {
    $modal.msgWarning('正在建设中，敬请期待')
  } else {
    location.href = row.path
  }
  navUserRef.value.hide()
}
const logout = async () => {
  await userStore.logout()
  router.push('/login')
}

</script>
<style lang="scss">
.el-popover.el-popper.nav-user-wrap {
  padding: 0px !important;
  margin: 0px !important;
  width: 420px !important;
  // height: 288px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  // inset: 60px auto auto auto !important;

  .nav-user-container {
    // height: 288px;
    margin: 20px 20px 10px 20px;
    overflow-y: auto;

    .profile-wrap {
      display: flex;
      align-items: center;

      img {
        width: 51px;
        height: 51px;
        border-radius: 50%;
      }

      .user-name {
        margin-left: 10px;
        font-size: 16px;
      }

      .noVip {
        width: 82px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: #D9D9D9;
        border-radius: 4px;
        color: #999999;
        margin: 0 10px;
      }

      .vip-icon {
        width: 20px;
        height: 20px;
        margin: 0 5px;
        border-radius: 0;
      }

    }

    .enter-center {
      display: flex;
      justify-content: flex-end;
      color: $primary-color;

      a {
        cursor: pointer;
        height: 48px;
      }
    }

    .line {
      width: 100%;
      height: 1px;
      background: #E8E8E8;
      margin: 0px;
    }

    .tool-wrap {
      .tool-title {
        font-size: 16px;
        color: #333333;
        font-weight: bold;
        margin: 20px 0px;
      }

      .tool-list {
        height: 90px;
        background: #F8F9FB;
        padding: 0px 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .tool-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;

          &:hover {
            .name {
              color: $primary-color;
              font-weight: bold;
            }
          }

          i {
            font-size: 22px;
            color: $primary-color;
          }

          .name {
            margin-top: 5px;
            font-size: 14px;
            font-weight: bold;
          }
        }
      }
    }
  }

}
</style>