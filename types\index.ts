// 请求响应数据
export interface IResponseData {
  code: number | string;
  message: string;
  data?: any;
  rows?: any[] | null | undefined;
  total?: number;
  [key: string]: any;
}

// 用户信息
export interface IUserInfo {
  userId: string;
  userName: string;
  nickName: string;
  realName: string;
  userType: string;
  email: string;
  phonenumber: string;
  [key: string]: any;
}

// 历史搜索记录
export interface IHistoryInfo {
  d: number; // id编号，时间戳到毫秒 Date.now()
  q: string; // 问题
  t: number; // 类型 （0：查标准、1：查公告、2：查法规、3：查计划）
}

// 官网公告
export interface INoticeInfo {
  id: string;
  title: string;
  summary: string;
  content: string;
  createTime: string;
  publishDate: string;
  [key: string]: any;
}
// 客服人员信息
export interface ICustomer {
  userId: string;
  userName: string;
}

// 标准信息
export interface IStandardInfo {
  standardCode: string;
  standardName: string;
  standardTypeName: string;
  publishDate: string;
  executeDate: string;
  standardType: string | number;
  standardStatus: number | string;
  standardNameEn?: string;
  repealDate?: string;
  standardTypeCodeGbName: string;
  standardTypeCodeIsoName: string;
  [key: string]: any;
}

// TC信息
export interface ITcInfo {
  committeeNumber: string;
  cnCommitteeName: string;
  currentSecretaryGeneral: string;
  sessionNumber: number;
  secretariatUnit: string;
  commissionerCnt: number;
  draftersList: string[];
  responsibleProfessionalScope: string;
  [key: string]: any;
}

// 样品信息
export interface ISampleInfo {
  sampleCode: string;
  sampleName: string;
  sampleStatus: number | string;
  sampleStatusName: string;
  valuingDate: string;
  validityEndDate: string;
  approvalDate: string;
  registryUnit: string;
  [key: string]: any;
}

// 专家信息
export interface IExpertInfo {
  commissionerName: string;
  workUnit: string;
  committeeNumberList: string;
  [key: string]: any;
}

// 公告信息
export interface IAnnouncementInfo {
  title: string;
  noticeCode: string;
  noticeTypeName: string;
  stdNum: number | string;
  publishDate: string;
  publishingUnit: string;
  [key: string]: any;
}

// 法律法规
export interface ILawInfo {
  title: string;
  publishDate: string;
  [key: string]: any;
}

// 计划标准
export interface IPlanInfo {
  planNumber: string;
  entryName: string;
  type: number | string;
  typeName: string;
  amend: number | string;
  amendName: string;
  projectStatus: string;
  projectStatusName: string;
  planReleaseDate: string;
  registryUnit: string;
  [key: string]: any;
}

// 标准体系
export interface ISystemInfo {
  subscription: number | string;
  systemName: string;
  hotTag: number | string;
  systemDescription: string;
  [key: string]: any;
}

// 标准知识
export interface IKnowledgeInfo {
  knowledgeName: string;
  introduction: string;
  publishDate: string;
  standardCodes: string;
  economicTypeNameList?: string[];
  [key: string]: any;
}

// 标准政策
export interface IPolicyInfo {
  policyTitle: string;
  fullRegionName: string;
  source: string;
  policyPublishDate: string;
  [key: string]: any;
}

// 填报
export interface IFillInInfo {
  fillingTheme: string;
  fillingStatus: string;
  fillingStatusName: string;
  fillingStartDate: string;
  fillingEndDate: string;
  initiatingDepartment: string;
  fullRegionName: string;
  fillingObject: string;
  [key: string]: any;
}

// 字典
export interface IDicts {
  dictValue: string | number;
  dictLabel: string | number;
  [key: string]: any;
}

// 权益包详细信息
export interface IBenefitPackageItem {
  id: number;
  originalPrice: number;
  discount: number;
  indate: string;
  type: 'annual' | 'monthly';
  name: string,
  number: number;
  [key: string]: any;
}

// 权益包列表
export interface IBenefitPackage {
  id: number;
  title: string;
  introduce: string;
  package: IBenefitPackageItem[];
  [key: string]: any;
}

// 服务项目
export interface IServiceItem {
  id: number;
  name: string;
  [key: string]: any;
}

// 会员服务列表
export interface IMemberPackage {
  id: number;
  originalPrice: number;
  discount: number;
  type?: 'annual' | 'monthly';
  package?: IServiceItem[];
  [key: string]: any;
}


