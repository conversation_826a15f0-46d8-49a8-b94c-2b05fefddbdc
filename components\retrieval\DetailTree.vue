<template>
  <div v-if="treeData && treeData.length > 0" class="tree-content">
    <div class="flex flex-ai-center mb-10">
      <span class="iconfont icon-wenzhang f-20 c-primary"></span>
      <span class="f-18 c-33 f-bold ml-10 overflow-ellipsis">{{ props.standardCode }}</span>
    </div>
    <el-tree
      ref="treeRef"
      node-key="id"
      empty-text="暂无数据"
      :data="treeData"
      :highlight-current="true"
      :expand-on-click-node="false"
      :default-expanded-keys="treeHighLight"
      :props="{ children: 'children', label: 'name' }"
      @node-click="handleNodeClick"
      class="tree-content-item"
    >
      <template #default="{ node, data }">
        <div class="flex flex-ai-center" style="width: 100%">
          <span v-if="node.level == 1" class="iconfont icon-levels f-16 c-primary"></span>
          <RetrievalToolTip :text="node.label" :className="isHighlight(data) ? 'c-ff0000' : ''" />
        </div>
      </template>
    </el-tree>
    <RetrievalTreePreview
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :standardName="props.standardName"
      :standardCode="props.standardCode"
      :standardId="standardId"
      :treeId="treeId"
      :searchValue="searchValue"
    />
  </div>
</template>

<script lang="ts" setup>
  import { getDomesticTree, getDomesticTreeHighLight } from '@/api/retrieval/domestic';
  import { type IResponseData } from '@/types';
  import { ElTree } from 'element-plus';

  const route = useRoute();

  const props = defineProps({
    standardName: {
      type: String,
      default: '',
    },
    standardCode: {
      type: [String, Number],
      default: '',
    },
  });

  const treeRef = ref<InstanceType<typeof ElTree>>();
  const dialogVisible = ref<boolean>(false);
  const standardId = ref<string | number>('');
  const treeData = ref([]);
  const treeHighLight = ref([]);
  const treeId = ref('');
  const searchValue = ref('');

  standardId.value = route.query.id as string | number;
  searchValue.value = route.query?.content ? decodeURI(route.query.content as string | '') : '';

  let treeRes = <IResponseData>await getDomesticTree(route.query.id as string | number);
  treeData.value = treeRes.data.tree || [];

  if (searchValue.value) {
    let treeHighLightRes = <IResponseData>await getDomesticTreeHighLight({
      standardId: route.query.id,
      title: searchValue.value,
    });
    treeHighLight.value = treeHighLightRes.data || [];
  }

  const isHighlight = (data: any) => {
    return treeHighLight.value.some(n => n === data.id);
  };

  const handleNodeClick = (node: any) => {
    if (!node.children || node.children.length == 0) {
      treeId.value = node.id;
      dialogVisible.value = true;
    }
  };
</script>

<style lang="scss" scoped>
  .tree {
    &-content {
      padding: 25px 15px 15px;
      box-sizing: border-box;
      border: 1px solid #e8e8e8;
      overflow: hidden;
      
      &-item {
        overflow-y: scroll;
        max-height: 600px;

        &::-webkit-scrollbar {
          width: 0;
        }
      }
    }
  }
</style>
