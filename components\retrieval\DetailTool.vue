<template>
  <div class="tool">
    <div v-if="props.isShowCollect" @click="handleTool('collect')" class="tool-item flex flex-center pointer">
      <div>
        <span class="iconfont f-24" :class="props.form.collectType ? 'icon-shoucang2 c-yellow' : 'icon-shoucang21 c-ff'"></span>
        <div class="mt-5">收藏</div>
      </div>
    </div>
    <div v-if="props.isShowTrusteeship" @click="handleTool('trusteeship')" class="tool-item flex flex-center pointer">
      <div>
        <span
          class="iconfont f-24"
          :class="props.form.trusteeshipType == 1 ? 'icon-kuangjituoguan c-yellow' : 'icon-tuoguan c-ff'"
        ></span>
        <div class="mt-5">托管</div>
      </div>
    </div>
    <div v-if="props.isShowFeedback" @click="handleTool('feedback')" class="tool-item flex flex-center pointer">
      <div>
        <span class="iconfont f-24 icon-baocuofankui c-ff"></span>
        <div class="mt-5">报错</div>
      </div>
    </div>
    <div @click="handleTool('backTop')" class="tool-item flex flex-center pointer">
      <div>
        <span class="iconfont f-24 icon-icon3 c-ff"></span>
        <div class="mt-5">返回顶部</div>
      </div>
    </div>
    <RetrievalFeedbackDialog v-if="dialogVisible" v-model:visible="dialogVisible" :type="props.type" :data="props.form" />
    <BxcLogin v-if="openLogin" v-model:open="openLogin" />
  </div>
</template>

<script lang="ts" setup>
  import { ElMessageBox } from 'element-plus';
  import { type IResponseData } from '@/types';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();
  const { $modal } = useNuxtApp();

  const props = defineProps({
    form: {
      required: true,
      type: Object,
    },
    isShowCollect: {
      type: Boolean,
      default: true,
    },
    isShowTrusteeship: {
      type: Boolean,
      default: true,
    },
    isShowFeedback: {
      type: Boolean,
      default: true,
    },
    type: {
      required: true,
      type: [Number, String],
    },
  });

  let timer: any;

  const toolList = computed(() => [
    {
      title: '收藏',
      icon: props.form.collectType ? 'icon-shoucang2 c-yellow' : 'icon-shoucang21 c-ff',
      toolName: 'collect',
    },
    {
      title: '托管',
      icon: props.form.trusteeshipType == 1 ? 'icon-kuangjituoguan c-yellow' : 'icon-tuoguan c-ff',
      toolName: 'trusteeship',
    },
    {
      title: '报错',
      icon: 'icon-baocuofankui c-ff',
      toolName: 'feedback',
    },
    {
      title: '返回顶部',
      icon: 'icon-icon3 c-ff',
      toolName: 'backTop',
    },
  ]);

  const openLogin = ref(false);
  const dialogVisible = ref(false);
  const isShowBackTop = ref(false);

  onMounted(() => {
    window.addEventListener('scroll', handleScroll);
  });

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
  });

  const handleScroll = () => {
    isShowBackTop.value = window.scrollY > 200;
  };

  const handleTool = async (type: string) => {
    switch (type) {
      case 'collect':
        if (!userStore.token) {
          openLogin.value = true;
        } else {
          let data = <IResponseData>await useHttp.post('/search/userCollectInfo/isCollect', {
            recordId: props.form.id,
            recordType: props.type,
            isCollect: props.form.collectType ? 0 : 1,
          });
          if (data.code == 200) {
            emit('updateData');
            $modal.msgSuccess(props.form.collectType ? '取消收藏成功！' : '收藏成功！');
          }
        }
        break;
      case 'trusteeship':
        if (!userStore.token) {
          openLogin.value = true;
        } else {
          if (props.form.trusteeshipType == 1) {
            ElMessageBox.confirm('确认取消托管标准【' + props.form.standardCode + '】？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
              lockScroll: false,
            })
              .then(() => {
                useHttp.delete('/search/trusteeshipManage/remove', { ids: [props.form.id] }).then(res => {
                  let data = res as IResponseData;
                  if (data.code == 200) {
                    $modal.msgSuccess('取消标准托管成功！');
                    emit('updateData');
                  }
                });
              })
              .catch(() => {});
          } else {
            let data = <IResponseData>await useHttp.post('/search/trusteeshipManage', {
              standardId: props.form.id,
              standardCode: props.form.standardCode,
              standardType: props.form.standardType,
            });
            if (data.code == 200) {
              emit('updateData');
              $modal.msgSuccess('标准托管成功！');
            }
          }
        }
        break;
      case 'feedback':
        dialogVisible.value = true;
        break;
      case 'backTop':
        if (document.documentElement.scrollTop > 20) {
          timer = setInterval(() => {
            let top = document.documentElement.scrollTop;
            document.documentElement.scrollTop = top -= 50;
            if (top <= 0) {
              clearInterval(timer);
            }
          }, 5);
        }
        break;
      default:
        break;
    }
  };

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped>
  .tool {
    position: fixed;
    top: 35%;
    right: 5px;
    border-radius: 5px;
    background-color: $primary-color;
    z-index: 999;

    &-item {
      font-size: 14px;
      color: #ffffff;
      width: 74px;
      height: 75px;
      text-align: center;
      margin-top: 1px;

      &:not(:first-child) {
        border-top: 1px solid rgba(255, 255, 255, 0.17);
      }
    }
  }

  .c-yellow {
    color: #f2a511;
  }
</style>
