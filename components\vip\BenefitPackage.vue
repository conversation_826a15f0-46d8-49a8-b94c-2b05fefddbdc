<template>
  <div v-show="activeTab == 1" class="package">
    <template v-if="benefitPackage.length > 0">
      <div class="package-top">
        <div class="f-24 c-primary f-bold">{{ benefitPackage[activePackageIndex].title }}</div>
        <div class="f-16 c-33 mt-10">
          套餐内容：支持解析
          <span class="c-ff0000 f-16 f-bold">
            {{ benefitPackage[activePackageIndex].package[activePackageInfoIndex].name }}
          </span>
          页
        </div>
      </div>
      <div class="package-content mt-40">
        <div
          @click="handlePackage(index)"
          v-for="(item, index) in benefitPackage"
          :key="index"
          class="package-content-item"
          :class="{ 'package-content-item-active': index == activePackageIndex }"
        >
          <div class="overflow-ellipsis">{{ item.introduce }}</div>
          <div>体验优质服务</div>
        </div>
      </div>
      <div v-if="benefitPackage[activePackageIndex].package.length > 0" class="package-content mt-10">
        <div
          @click="handlePackageInfo(index)"
          v-for="(item, index) in benefitPackage[activePackageIndex].package"
          :key="index"
          class="package-content-info"
          :class="{ 'package-content-info-active': index == activePackageInfoIndex }"
        >
          <div class="f-16 c-99 text-center">
            <span class="f-36 c-primary f-bold">{{ item.originalPrice - item.discount }}</span>
            /元
          </div>
          <div class="package-content-info-indate">有效期{{ item.indate + item.type == 'annual' ? '年' : '月' }}</div>
          <div @click.stop="handlePay(item)" class="package-content-info-btn">立即开通</div>
        </div>
      </div>
      <BxcEmpty v-else />
    </template>
    <BxcEmpty v-else />
  </div>
</template>

<script setup lang="ts">
  import type { IBenefitPackage, IBenefitPackageItem } from '@/types/index';

  const emit = defineEmits(['pay']);

  const props = withDefaults(
    defineProps<{
      benefitPackage: IBenefitPackage[];
    }>(),
    {
      benefitPackage: () => [],
    }
  );

  const { benefitPackage } = toRefs(props);

  const activeTab = ref<number>(0);
  const activePackageIndex = ref<number>(0);
  const activePackageInfoIndex = ref<number>(0);

  const handlePackage = (index: number) => {
    activePackageIndex.value = index;
    activePackageInfoIndex.value = 0;
  };

  const handlePackageInfo = (index: number) => {
    activePackageInfoIndex.value = index;
  };

  const handlePay = (row: IBenefitPackageItem) => {
    let obj = { ...benefitPackage.value[activePackageIndex.value], childrenId: row.id };
    emit('pay', obj);
  };
</script>

<style scoped lang="scss">
  .package {
    width: 100%;
    padding: 35px 25px;
    box-sizing: border-box;
    background: #fff;

    &-top {
      width: 100%;
      height: 110px;
      padding: 30px 40px;
      box-sizing: border-box;
      background: url('@/assets/images/vip/package_bg.png') no-repeat center;
      background-size: 100% 100%;
    }

    &-content {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      &-item {
        width: calc((100% - 80px) / 5);
        margin: 0 20px 20px 0;
        border-radius: 5px;
        border: 1px solid #e6e6e6;
        font-size: 16px;
        color: #333;
        line-height: 26px;
        text-align: center;
        padding: 25px 40px;
        box-sizing: border-box;
        cursor: pointer;

        &:nth-child(5n) {
          margin-right: 0;
        }

        &-active {
          border-color: $primary-color;
          background-color: $primary-color;
          color: #fff;
        }
      }

      &-info {
        width: calc((100% - 60px) / 4);
        margin: 0 20px 20px 0;
        border-radius: 5px;
        border: 1px solid #e3e3e3;
        padding: 30px 50px 25px;
        box-sizing: border-box;
        cursor: pointer;

        &:nth-child(4n) {
          margin-right: 0;
        }

        &-active {
          border-color: $primary-color;
          background-color: #f0f7ff;
        }

        &-indate {
          font-size: 14px;
          color: $primary-color;
          text-align: center;
          width: 110px;
          height: 26px;
          line-height: 26px;
          background: #ecf5ff;
          border-radius: 3px;
          border: 1px solid $primary-color;
          margin: 10px auto 20px;
        }

        &-btn {
          width: 158px;
          height: 36px;
          line-height: 36px;
          background: linear-gradient(-90deg, #ae00ff, #045cff);
          box-shadow: 0px 4px 3px 0px rgba(24, 53, 108, 0.26);
          border-radius: 5px;
          margin: 0 auto;
          text-align: center;
          color: #fff;
          font-size: 14px;
        }
      }
    }
  }
</style>
