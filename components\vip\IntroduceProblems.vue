<template>
  <div class="main-wrap">
    <div class="introduce">
      <div class="c-primary f-20 f-bold">什么是标信查会员</div>
      <div class="mt-15">
        1.标信查致力于为用户提供最全面、最专业、最便捷的标准查询、管理、学习及应用服务。为了更好地满足广大用户的需求，特别推出了个人会员与企业会员两种会员服务，并根据不同会员等级提供丰富多样的会员权益。
      </div>
      <div>2.标信查会员分为个人会员与企业会员两类，同时提供单项权益包服务，以满足不同用户的个性化需求。</div>
      <div>
        3.当您完成会员费用支付后，会员资格立即生效。我们将立即为您开通相应的会员服务，并确保您能够享受到会员专属的各项权益和服务。
      </div>
      <div>4.权益包服务购买后，权益时间单独计算，使用时，会优先使用临近期效的服务次数</div>
    </div>
    <div class="question">
      <div class="flex flex-ai-center">
        <img src="@/assets/images/vip/question.png" alt="" class="question-icon" />
        <span class="f-bold f-22">常见问题</span>
      </div>
      <template v-for="(item, index) in questionList" :key="index">
        <div class="mt-30 f-bold f-18">{{ item.q }}</div>
        <div class="mt-15 c-99 f-14">{{ item.a }}</div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  const questionList = ref([
    {
      q: 'Q：平台有哪些会员套餐可供选择？',
      a: 'A：标信查首页右上角的“会员”栏目中，包含了平台在售的各类会员套餐及权益包。用户可根据自身需求，如个人使用或企业团队使用，以及所需的服务级别，选择合适的套餐进行线上自助下单。对于需要企业定制化服务的用户，可拨打400-109-7887联系我们的客服人员，进行线下购买和咨询。标信查平台将竭诚为您提供最适合的会员套餐和服务方案。',
    },
    {
      q: 'Q：会员购买后是否可以退款？',
      a: 'A：抱歉，标信查会员服务一经购买，若无特殊情况（如服务无法正常使用且客服无法提供替代解决方案），暂不支持退款。请您在购买前仔细阅读会员条款，并谨慎考虑自身需求后再做决定。',
    },
    {
      q: 'Q：标信查会员分为哪些类型？不同会员之间有什么区别？',
      a: 'A：标信查会员分为个人会员与企业会员两大类，同时提供单项权益包服务以满足不同用户的个性化需求。个人会员专为个人用户设计，享有基本的会员权益，如标准查询、标准托管等；企业会员则针对企业用户，提供全方位的标准化解决方案，包括标准查重、标准查新、标准管理等高级服务，以及专属顾问、定制报告等更多企业专属的服务和优惠。',
    },
    {
      q: 'Q：购买会员后如何获取发票？',
      a: 'A：购买会员后，您可以在网站右侧的“咨询”窗口联系客服人员获取。',
    },
  ]);
</script>

<style scoped lang="scss">
  .main-wrap {
    padding: 60px 0 65px;
    box-sizing: border-box;

    .introduce {
      position: relative;
      min-height: 308px;
      background: url('@/assets/images/vip/introduce.png') no-repeat center;
      background-size: 100% 100%;
      margin-top: 100px;
      padding: 96px 300px 0 25px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      line-height: 28px;
    }

    .question {
      margin-top: 100px;

      &-icon {
        height: 26px;
        margin-right: 15px;
      }
    }
  }
</style>
