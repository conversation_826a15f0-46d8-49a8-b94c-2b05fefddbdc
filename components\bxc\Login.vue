<template>
  <el-dialog
    :append-to-body="true"
    :lock-scroll="false"
    :modelValue="open"
    title=""
    width="780px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
    class="pop-login"
  >
    <div class="bxc-login-wrap">
      <div class="bxc-login-left">
        <img src="@/assets/images/login/pop-login-logo.png" alt="">
        <ul class="tip-list">
          <li class="tip-title">全方位的标准智能化检索与应用</li>
          <li class="tip-item"><i class="iconfont icon-icon_duihao-mian" /><span>全量标准数据检索</span></li>
          <li class="tip-item"><i class="iconfont icon-icon_duihao-mian" /><span>标准文本内容检索</span></li>
          <li class="tip-item"><i class="iconfont icon-icon_duihao-mian" /><span>标准追踪预警</span></li>
          <li class="tip-item"><i class="iconfont icon-icon_duihao-mian" /><span>标准应用管理</span></li>
          <li class="tip-item"><i class="iconfont icon-icon_duihao-mian" /><span>标准智能大模型</span></li>
        </ul>
      </div>
      <div class="bxc-login-right">
        <div class="login-form-wrap">
          <ul class="login-type-list">
            <li v-for="(item,index) in loginTypeList" :key="index" class="login-type-item" :class="{actived: currentLoginType == index}" @click="changeLoginType(index)">
              {{ item }}
            </li>
          </ul>
          <el-form ref="loginRef" :model="loginForm" :rules="formRules" class="login-form" :validate-on-rule-change="false">
            <LoginAccountFormItem v-show="currentLoginType == 0" v-model:loginForm="loginForm" @handleLogin="handleLogin"/>
            <LoginMobileFormItem v-show="currentLoginType == 1" v-model:loginForm="loginForm" :loginRef="loginRef" @handleLogin="handleLogin" @handleVerify="handleVerify" />
            <el-form-item class="login-btn" style="width:100%;">
              <el-button
                :loading="loading"
                size="large"
                type="primary"
                style="width:100%;"
                @click.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
            </el-form-item>
            <div class="tip-wrap">
              <span>没有账号？</span>
              <NuxtLink to="/register" class="register">立即注册</NuxtLink>
            </div>
          </el-form>
        </div>
      </div>

      <div class="close">
        <i class="iconfont icon-guanbi" @click="close" />
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store/userStore';
import type { FormInstance } from 'element-plus';

const props = defineProps(["open"]);
const emit = defineEmits(["update:open", "success"]);

const loginRef = ref<FormInstance>();
const currentLoginType = ref(0);
const loading = ref(false);

const loginForm = ref({
  username: "",
  password: "",
  phonenumber: "",
  smsCode: ""
});
const loginTypeList = ['账号密码登录','验证码登录']

const userStore = useUserStore()
const router = useRouter()

const loginRules = {
  username: [
    { required: true, trigger: "blur", message: "请输入账户名称" },
    { pattern: userNamePattern, trigger: "blur", message: "请输入有效登录账户" }
  ],
  password: [
    { required: true, trigger: "blur", message: "请输入登录密码" },
    { pattern: passwordPattern, trigger: "blur", message: "请输入有效登录密码" }
  ],
};
const mobileRules = {
  phonenumber: [
    { required: true, trigger: "blur", message: "请输入手机号码" },
    { pattern: mobileValidPattern, trigger: "blur", message: "请输入正确的手机号码" }
  ],
  smsCode: [{ required: true, trigger: "blur", message: "请输入验证码" }],
}
const formRules = computed(() => {
  return currentLoginType.value == 0 ? loginRules : mobileRules;
})

const changeLoginType = (index: number) => {
  currentLoginType.value = index;
}
const handleVerify = () => {
  
}
const handleLogin = () => {
  loginRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      if(currentLoginType.value == 0){ // 账号登录
        userStore.login(loginForm.value).then(res =>{
          reloadNuxtApp()
          close()
          emit('success') 
        }).catch((error)=>{
          loading.value = false;
        })
      }else{ // 手机登录
        userStore.mobileLogin(loginForm.value).then(res =>{
          reloadNuxtApp()
          close()
          emit('success') 
        }).catch((error)=>{
          loading.value = false;
        })
      }
    }
  })
}
const close = () => {
  emit("update:open", false);
};
</script>
<style lang="scss">
.pop-login{
  padding: 0px !important;
  margin: 0px !important;
  box-sizing: border-box;
  .el-dialog__header.show-close{
    display: none !important;
  }
  .el-dialog__body{
    padding: 0px !important;
    margin: 0px !important;
    border-radius: 0px !important;
    overflow: hidden !important;

  }
  .el-dialog__footer {
    border-radius: 0 !important;
  }
  .bxc-login-wrap{
    position: relative;
    display: flex;
    height: 450px;
    .bxc-login-left{
      width: 310px;
      background: url('@/assets/images/login/pop-login-bg.png') no-repeat center;
      background-size: 100% 100%;
      padding: 20px;
      box-sizing: border-box;
      
      .tip-list{
        margin-left: 25px;
        margin-top: 70px;
        color: #FFFFFF;
        font-size: 14px;
        .tip-title{
          font-weight: bold;
          font-size: 16px;
        }
        .tip-item{
          margin-top: 20px;
          span {
            margin-left: 10px;

          }
        }
      }
    }
    .bxc-login-right{
      flex: 1;
      .login-form-wrap{
        width: 100%;
        height: 100%;
        background: #FFFFFF;
        box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.16);
        border-radius: 8px;
        box-sizing: border-box;
        padding: 40px;
        .login-type-list{
          margin-top: 40px;
          display: flex;
          justify-content: center;
          box-sizing: border-box;
          gap: 60px;
          font-size: 20px;
          .login-type-item{
            font-size: 20px;
            color: #333333;
            cursor: pointer;
            &.actived{
              font-weight: bold;
              color: $primary-color;
              position: relative;
              &::after {
                content: '';
                position: absolute;
                left: 50%;
                top: 35px;
                transform: translateX(-50%); /* 使伪元素水平居中 */
                width: 50%; /* 自定义长度的一半 */
                height: 5px; /* 自定义border-bottom的厚度 */
                background: $primary-color; /* 自定义border-bottom的颜色 */
                border-radius: 3px;
              }
            }
          }
          
        }
        .login-form{
          margin-top: 50px;
          .login-btn{
            margin-top: 50px;
          }
          .tip-wrap{
            display: flex;
            justify-content: center;
            margin-top: 10px;
            font-size: 14px;
            
            .register{
              color: $primary-color !important;
              text-decoration: underline;
            }

          }
        }
        .el-form-item {
          width: 100%;
          height: 48px;
          line-height: 48px;
          border: none;
          margin-right: 0px !important;
          .el-input{
            height: 48px;
            line-height: 48px;
            border: none;
            
            font-size: 14px;
          }
          .el-input__wrapper,.el-input__inner{
            background: #F6F7F9;
            border: none;
            box-shadow: none;
            border-radius: 3px;
          }
          .el-input__prefix{
            margin-left: 5px;
            // width: 30px;
            .p-num{
              display: flex;
              align-items: center;
              span{
                font-size: 16px;
                color: #333333;
              }
              .line{
                width: 1px;
                height: 28px;
                background: #DCDCDC;
                margin: 0 10px;
              }
            }
          }
          .el-input-group__append{
            padding: 0px;
            .verify{
              padding: 0 12px;
              text-align: center;
              background: $primary-color !important;
              font-size: 14px;
              color: #ffffff !important;
              cursor: pointer;
            }
            .down-timer{
              padding: 0 12px;
              text-align: center;
              background: #89b3ff !important;
              font-size: 14px;
              color: #ffffff !important;
            }
          }
          
          .el-button{
            height: 48px;
            font-size: 18px;
            background: $primary-color;
            border-radius: 3px;
            font-weight: bold;
            border-color: $primary-color !important;
          }
        }
      }
    }
    .close{
      position: absolute;
      right: 10px;
      top: 20px;
      cursor: pointer;

    }
  }
}

</style>
